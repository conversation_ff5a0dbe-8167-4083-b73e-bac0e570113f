<template>
  <div class="java-chapter9">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第九章：Kotlin</h1>
            <p class="chapter-subtitle">Modern JVM Language</p>
            <p class="chapter-description">
              探索现代化JVM语言Kotlin：从设计哲学到实践应用，掌握这门Java最亲密战友的核心特性与最佳实践
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: Kotlin 简介与核心理念 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="Kotlin 简介与核心理念 (Introduction &amp; Philosophy)"
                :concept-data="kotlinIntroductionData"
                @interaction="handleInteraction"
              >
                <div class="kotlin-introduction-showcase">
                  <h3>🎯 Kotlin：Java 的现代化战友</h3>
                  <p class="intro-text">
                    Kotlin 不是为了颠覆 Java，而是作为 Java 最亲密的"战友"和"增强补丁"而生。
                    它解决了 Java
                    因背负历史包袱而难以解决的许多痛点，让我们写出更简洁、更安全、也更富表现力的代码。
                  </p>

                  <div class="design-philosophy">
                    <h4>🏗️ 核心设计哲学</h4>
                    <div class="philosophy-grid">
                      <div class="philosophy-card">
                        <div class="card-icon">🎯</div>
                        <h5>务实主义 (Pragmatism)</h5>
                        <p>
                          每一个特性都旨在解决 Java
                          开发中的真实痛点，不追求学术完美，而是追求工程好用
                        </p>
                      </div>
                      <div class="philosophy-card">
                        <div class="card-icon">🔗</div>
                        <h5>100% 互操作性</h5>
                        <p>与 Java 完全兼容，可以逐个文件迁移，所有 Java 库和框架都能直接使用</p>
                      </div>
                      <div class="philosophy-card">
                        <div class="card-icon">🛡️</div>
                        <h5>类型安全</h5>
                        <p>在类型系统层面根除 NullPointerException，提供编译期安全保障</p>
                      </div>
                      <div class="philosophy-card">
                        <div class="card-icon">✨</div>
                        <h5>简洁表达</h5>
                        <p>大幅减少模板代码，让核心业务逻辑更加突出和清晰</p>
                      </div>
                    </div>
                  </div>

                  <div class="comparison-showcase">
                    <h4>📊 Java vs Kotlin：一目了然的对比</h4>
                    <div class="code-comparison">
                      <div class="comparison-item">
                        <h5>Java 版本</h5>
                        <div class="code-block">
                          <pre><code>public class User {
    private final String name;
    private final int age;
    
    public User(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() { return name; }
    public int getAge() { return age; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return age == user.age && Objects.equals(name, user.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, age);
    }
    
    @Override
    public String toString() {
        return "User{name='" + name + "', age=" + age + "}";
    }
}</code></pre>
                        </div>
                        <div class="code-stats">
                          <span class="stat-item">📏 25+ 行代码</span>
                          <span class="stat-item">⚠️ 容易出错</span>
                        </div>
                      </div>
                      <div class="comparison-item">
                        <h5>Kotlin 版本</h5>
                        <div class="code-block">
                          <pre><code>data class User(val name: String, val age: Int)</code></pre>
                        </div>
                        <div class="code-stats">
                          <span class="stat-item">📏 1 行代码</span>
                          <span class="stat-item">✅ 自动生成所有方法</span>
                          <span class="stat-item">🛡️ 类型安全</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 便利性与简洁性 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="便利性与简洁性 (Convenience &amp; Conciseness)"
                :concept-data="convenienceData"
                @interaction="handleInteraction"
              >
                <div class="convenience-showcase">
                  <h3>✨ 用更少的代码，干同样的事</h3>
                  <p class="intro-text">
                    Kotlin 通过引入一系列语法糖和现代语言特性，大幅减少编写 Java
                    代码时所需的"模板代码"， 让代码更紧凑、更易读，同时保持清晰的业务逻辑表达。
                  </p>

                  <div class="feature-showcase">
                    <div class="feature-card">
                      <h4>🔧 val/var：更智能的变量声明</h4>
                      <div class="feature-comparison">
                        <div class="before-after">
                          <div class="before">
                            <h6>Java</h6>
                            <pre><code>final String name = "Kotlin";
String message = "Hello";
message = "Hi";</code></pre>
                          </div>
                          <div class="after">
                            <h6>Kotlin</h6>
                            <pre><code>val name = "Kotlin"  // 不可变
var message = "Hello" // 可变
message = "Hi"</code></pre>
                          </div>
                        </div>
                      </div>
                      <div class="feature-benefits">
                        <span class="benefit">🎯 类型推断</span>
                        <span class="benefit">🛡️ 鼓励不可变性</span>
                        <span class="benefit">📝 代码更简洁</span>
                      </div>
                    </div>

                    <div class="feature-card">
                      <h4>🎭 表达式化控制流</h4>
                      <div class="feature-comparison">
                        <div class="before-after">
                          <div class="before">
                            <h6>Java</h6>
                            <pre><code>String grade;
if (score &gt;= 90) {
    grade = "A";
} else if (score &gt;= 60) {
    grade = "B";
} else {
    grade = "C";
}</code></pre>
                          </div>
                          <div class="after">
                            <h6>Kotlin</h6>
                            <pre><code>val grade = if (score &gt;= 90) "A" 
           else if (score &gt;= 60) "B" 
           else "C"</code></pre>
                          </div>
                        </div>
                      </div>
                      <div class="feature-benefits">
                        <span class="benefit">🎯 表达式返回值</span>
                        <span class="benefit">📝 减少重复赋值</span>
                        <span class="benefit">🔒 避免未初始化变量</span>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：滥用隐式参数 `it` 导致可读性下降</h4>
                        <p>
                          一个 Java 项目迁移到 Kotlin
                          后，团队成员发现虽然代码行数减少了，但代码变得难以理解。
                          尤其是集合操作，大量使用了 `it`
                          作为隐式参数的链式调用，当链条很长、逻辑复杂时， 新手很难快速看出每个 `it`
                          指代的到底是什么。
                        </p>
                        <div class="code-example">
                          <h6>❌ 问题代码</h6>
                          <pre><code>list.filter { it > 0 }
    .map { it.toString() }
    .forEach {
        println("Processing: $it")
        anotherList.filter { it > 0 } // 这里的 it 是哪个？
    }</code></pre>
                        </div>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          滥用了隐式参数 `it`。`it` 是 Kotlin 为只有一个参数的 Lambda
                          提供的便利语法， 旨在简化代码。但在嵌套 Lambda 或长调用链中，多个 `it`
                          的存在会造成严重的指代不明和可读性下降。
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>业界最佳实践：制定明确的编码规范</h4>
                        <div class="solution-guidelines">
                          <div class="guideline">
                            <h5>✅ 简单场景：允许使用 `it`</h5>
                            <p>对于单层、且逻辑极度简单的 Lambda</p>
                            <div class="code-example">
                              <pre><code>names.map { it.uppercase() }
numbers.filter { it > 0 }</code></pre>
                            </div>
                          </div>
                          <div class="guideline">
                            <h5>🎯 复杂场景：必须显式命名</h5>
                            <p>在任何嵌套的 Lambda 中，或者当 Lambda 体超过一行时</p>
                            <div class="code-example">
                              <pre><code>// 推荐写法
list.forEach { item ->
    println("Processing item: $item")
    anotherList.filter { number -> number > 0 }
}</code></pre>
                            </div>
                          </div>
                        </div>
                        <div class="best-practice-note">
                          <strong>核心原则：</strong>清晰性永远优先于简洁性。 合理使用显式命名是区分
                          Kotlin 新手和专家的重要标志。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 焕然一新的类与对象 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="焕然一新的类与对象 (A Fresh Take on Classes &amp; Objects)"
                :concept-data="classesObjectsData"
                @interaction="handleInteraction"
              >
                <div class="classes-objects-showcase">
                  <h3>🏗️ 从毛坯房到精装房的升级</h3>
                  <p class="intro-text">
                    Kotlin 对 Java
                    的面向对象模型进行了现代化改造，把原来需要你自己动手刷墙、铺地板、买家具的"毛坯房"(Java
                    Class)， 升级成了可以直接拎包入住的"精装房"(data class)。
                  </p>

                  <div class="feature-comparison-grid">
                    <div class="feature-item">
                      <h4>📦 Data Class：一行代码的奇迹</h4>
                      <div class="comparison-showcase">
                        <div class="java-version">
                          <h5>Java 版本 (25+ 行)</h5>
                          <div class="code-block">
                            <pre><code>public class User {
    private final String name;
    private final int age;

    public User(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public String getName() { return name; }
    public int getAge() { return age; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return age == user.age &&
               Objects.equals(name, user.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, age);
    }

    @Override
    public String toString() {
        return "User{name='" + name + "', age=" + age + "}";
    }
}</code></pre>
                          </div>
                        </div>
                        <div class="kotlin-version">
                          <h5>Kotlin 版本 (1 行)</h5>
                          <div class="code-block">
                            <pre><code>data class User(val name: String, val age: Int)</code></pre>
                          </div>
                          <div class="auto-generated">
                            <h6>✨ 自动生成的功能</h6>
                            <ul>
                              <li>🏗️ 主构造函数</li>
                              <li>📖 getter 方法 (getName, getAge)</li>
                              <li>⚖️ equals() 和 hashCode()</li>
                              <li>📝 toString()</li>
                              <li>📋 copy() 方法</li>
                              <li>🔧 componentN() 解构方法</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="feature-item">
                      <h4>🔒 默认关闭继承：安全第一</h4>
                      <div class="inheritance-demo">
                        <div class="concept-explanation">
                          <p>
                            与 Java 相反，Kotlin 的类和方法默认是 <code>final</code> 的，必须用
                            <code>open</code> 关键字才能被继承或重写。
                          </p>
                        </div>
                        <div class="code-comparison">
                          <div class="before-after">
                            <div class="before">
                              <h6>Java：默认开放</h6>
                              <pre><code>// 任何类都可以被继承
public class BaseService {
    public void process() { /* ... */ }
}

// 容易导致脆弱的基类问题</code></pre>
                            </div>
                            <div class="after">
                              <h6>Kotlin：显式声明</h6>
                              <pre><code>// 必须明确声明可继承
open class BaseService {
    open fun process() { /* ... */ }
}

// 强迫开发者思考设计意图</code></pre>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="feature-item">
                      <h4>🎯 属性：告别 getter/setter 地狱</h4>
                      <div class="properties-demo">
                        <div class="code-example">
                          <h6>Kotlin 属性的强大功能</h6>
                          <pre><code>class Person(val name: String) {
    var age: Int = 0
        set(value) {
            if (value >= 0) field = value
            else throw IllegalArgumentException("年龄不能为负数")
        }

    val isAdult: Boolean
        get() = age >= 18

    // 使用
    val person = Person("Alice")
    person.age = 25  // 调用 setter
    println(person.isAdult)  // 调用 getter
}</code></pre>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：手动维护 equals/hashCode 的一致性失败</h4>
                        <p>
                          在一个 Java 项目中，核心的 Transaction 对象被用作 HashMap 的 key。
                          后来开发者修改了 Transaction 类添加业务字段，但忘记同步更新 hashCode() 和
                          equals() 方法， 导致内容相同的两个 Transaction 对象被认为是不同的
                          Key，引发严重的数据错乱。
                        </p>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          手动维护 equals() 和 hashCode() 的一致性是极其脆弱和不可靠的。
                          只要类的字段发生变化，这两个方法就必须同步修改，这是一个极易被遗忘的人为错误点。
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>Kotlin 解决方案：data class 一劳永逸</h4>
                        <div class="solution-demo">
                          <pre><code>// 迁移到 Kotlin
data class Transaction(
    val id: Long,
    val amount: Double,
    val currency: String,
    val timestamp: LocalDateTime  // 新增字段
)

// 编译器自动保证 equals/hashCode 的正确性
// 任何字段的增删改都会自动反映到这两个方法中</code></pre>
                        </div>
                        <div class="benefits-list">
                          <h6>✅ 核心优势</h6>
                          <ul>
                            <li>🛡️ 编译器保证正确性</li>
                            <li>🔄 自动同步字段变化</li>
                            <li>🚫 彻底消除人为错误</li>
                            <li>📝 代码简洁易维护</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 编码安全感：空安全与智能类型转换 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="编码安全感：空安全与智能类型转换 (Code Safety: Null Safety &amp; Smart Casting)"
                :concept-data="nullSafetyData"
                @interaction="handleInteraction"
              >
                <div class="null-safety-showcase">
                  <h3>🛡️ 根除十亿美元的错误</h3>
                  <p class="intro-text">
                    Kotlin 在类型系统层面区分了"可空类型"和"不可空类型"，编译器强制你处理所有可能为
                    null 的情况， 将 NullPointerException 这一"价值十亿美元的错误"在编译期根除。
                  </p>

                  <div class="safety-features">
                    <div class="feature-section">
                      <h4>🔒 空安全：给变量上保险</h4>
                      <div class="null-safety-demo">
                        <div class="type-system">
                          <div class="type-card non-null">
                            <h5>不可空类型 (Non-null)</h5>
                            <div class="code-example">
                              <pre><code>var name: String = "Kotlin"
// name = null  // 编译错误！
println(name.length)  // 安全调用</code></pre>
                            </div>
                            <div class="type-guarantee">✅ 编译器保证永不为空</div>
                          </div>
                          <div class="type-card nullable">
                            <h5>可空类型 (Nullable)</h5>
                            <div class="code-example">
                              <pre><code>var name: String? = "Maybe"
name = null  // 合法
// println(name.length)  // 编译错误！</code></pre>
                            </div>
                            <div class="type-guarantee">⚠️ 必须显式处理空值</div>
                          </div>
                        </div>

                        <div class="safe-operations">
                          <h5>🛠️ 安全操作符</h5>
                          <div class="operator-grid">
                            <div class="operator-card">
                              <h6>?. 安全调用</h6>
                              <div class="code-example">
                                <pre><code>val length = name?.length
// 如果 name 不为 null，返回长度
// 如果 name 为 null，返回 null</code></pre>
                              </div>
                              <div class="analogy">💡 像戴着绝缘手套摸电线</div>
                            </div>
                            <div class="operator-card">
                              <h6>?: Elvis 操作符</h6>
                              <div class="code-example">
                                <pre><code>val length = name?.length ?: -1
// 如果左边为 null，返回右边的默认值</code></pre>
                              </div>
                              <div class="analogy">💡 先用验电笔测试，不带电就操作备用开关</div>
                            </div>
                            <div class="operator-card warning">
                              <h6>!! 非空断言 (慎用!)</h6>
                              <div class="code-example">
                                <pre><code>val length = name!!.length
// 我向编译器保证这里绝对不为 null
// 如果为 null，抛出 NPE</code></pre>
                              </div>
                              <div class="analogy">⚠️ 拍胸脯说"我赌它不带电"然后徒手摸</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="feature-section">
                      <h4>🧠 智能类型转换：编译器的好记性</h4>
                      <div class="smart-casting-demo">
                        <div class="code-example">
                          <h6>自动类型转换示例</h6>
                          <pre><code>fun process(obj: Any) {
    if (obj is String) {
        // 在这个代码块内，obj 被智能转换为 String 类型
        println(obj.length)  // 无需 (obj as String).length
        println(obj.uppercase())  // 直接调用 String 方法
    }

    if (obj is List&lt;*&gt; && obj.isNotEmpty()) {
        // obj 被智能转换为非空的 List
        println(obj.first())  // 安全调用
    }
}</code></pre>
                        </div>
                        <div class="smart-casting-explanation">
                          <p>
                            <strong>类比：</strong>你检查一个人的身份证确认他是"张三"，
                            接下来对话时直接喊"张三"就行，不需要每次都说"那个身份证上写着张三的人"。
                            编译器就是那个记性很好的旁观者。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：与 Java 系统集成时的空值处理失误</h4>
                        <p>
                          在与老旧 Java 系统集成的项目中，Kotlin 模块调用返回 User 对象的 Java
                          方法。 开发者为了快速完成功能，直接使用非空断言：
                          <code>val user = legacyApi.getUser(id)!!</code>
                          在特定场景下，Java API 返回了 null，导致生产环境抛出 NPE。
                        </p>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          在与外部系统边界上盲目信任返回的数据。Kotlin
                          的空安全系统只能保护内部世界。 与 Java 交互时，所有来自 Java
                          的引用都被视为"平台类型"(Platform Type)， 如
                          <code>String!</code>，意味着"我不知道它是否可空，由你决定"。
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>业界最佳实践：在系统边界进行严格处理</h4>
                        <div class="solution-steps">
                          <div class="step">
                            <h5>1. 定义明确的可空 Kotlin 数据模型</h5>
                            <div class="code-example">
                              <pre><code>// 明确字段的可空性
data class KotlinUser(
    val name: String,
    val address: Address?  // 明确标记可空
)</code></pre>
                            </div>
                          </div>
                          <div class="step">
                            <h5>2. 创建适配层进行安全转换</h5>
                            <div class="code-example">
                              <pre><code>fun findUser(id: String): KotlinUser? {
    val javaUser: JavaUser? = legacyApi.getUser(id) // 接收时就视为可空

    // 在边界处进行检查和转换
    return javaUser?.let { ju ->
        val kotlinAddress = ju.address?.let { ja ->
            Address(ja.city)
        }
        KotlinUser(ju.name ?: "Unknown", kotlinAddress)
    }
}</code></pre>
                            </div>
                          </div>
                          <div class="step">
                            <h5>3. 业务逻辑只依赖安全的内部模型</h5>
                            <p>
                              应用的其他部分只使用 findUser 方法和
                              KotlinUser，享受完整的空安全保护。
                            </p>
                          </div>
                        </div>
                        <div class="best-practice-note">
                          <strong>核心原则：</strong>"在边界处校验，在核心处信任" ——
                          构建健壮系统的关键原则。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 思维导图总结 -->
            <section class="mindmap-section">
              <div class="mindmap-container">
                <h3>🧠 第九章知识体系思维导图</h3>
                <p class="mindmap-description">从"更好用"到"更安全"再到"更强大"的清晰演进路径</p>
                <div id="kotlin-mindmap" class="mindmap-canvas"></div>
                <div class="mindmap-controls">
                  <button @click="renderMindMap" class="mindmap-button">🔄 重新渲染</button>
                  <button @click="downloadMindMap" class="mindmap-button">💾 下载图片</button>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: 'Kotlin 简介与核心理念',
    description: '理解 Kotlin 的设计哲学和核心价值',
  },
  {
    title: '便利性与简洁性',
    description: '探索 Kotlin 如何简化 Java 的冗余代码',
  },
  {
    title: '焕然一新的类与对象',
    description: '学习 Kotlin 的现代化面向对象特性',
  },
  {
    title: '编码安全感：空安全与智能类型转换',
    description: '掌握 Kotlin 的类型安全机制',
  },
  {
    title: '现代并发模型：协程',
    description: '初探 Kotlin 的轻量级并发方案',
  },
  {
    title: '无缝集成：与 Java 的互操作性',
    description: '学习 Kotlin 与 Java 的完美共存',
  },
]

// 概念数据
const kotlinIntroductionData = {
  keyPoints: [
    'Kotlin 是 Java 的现代化增强补丁，不是替代品',
    '务实主义设计哲学：解决真实痛点而非追求学术完美',
    '100% Java 互操作性，可以逐步迁移现有项目',
    '类型安全设计，在编译期根除 NullPointerException',
    '大幅减少模板代码，提升开发效率和代码可读性',
  ],
  interactiveElements: [
    { type: 'java-kotlin-converter', label: '🔄 Java/Kotlin 代码转换器' },
    { type: 'interop-demo', label: '🔗 互操作性演示' },
  ],
}

const convenienceData = {
  keyPoints: [
    'val/var 关键字提供更智能的变量声明和类型推断',
    '表达式化控制流让代码更简洁和安全',
    '约定优于配置的设计减少样板代码',
    '函数式编程特性增强代码表达力',
    '合理使用简洁特性，清晰性优先于简洁性',
  ],
  interactiveElements: [
    { type: 'syntax-comparison', label: '📊 语法对比工具' },
    { type: 'code-golf', label: '⛳ 代码简化挑战' },
  ],
}

const classesObjectsData = {
  keyPoints: [
    'data class 一行代码自动生成 equals、hashCode、toString 等方法',
    '属性(Properties)替代 Java 的字段和 getter/setter 模式',
    '主构造函数提供简洁的类定义语法',
    '默认 final 设计，需要 open 关键字才能继承',
    '编译器保证 data class 的方法与字段同步，消除人为错误',
  ],
  interactiveElements: [
    { type: 'data-class-generator', label: '🏗️ Data Class 生成器' },
    { type: 'inheritance-demo', label: '🔒 继承安全演示' },
  ],
}

const nullSafetyData = {
  keyPoints: [
    '类型系统区分可空类型(String?)和不可空类型(String)',
    '编译器强制处理所有可能为 null 的情况',
    '安全调用(?.)、Elvis操作符(?:)提供优雅的空值处理',
    '智能类型转换自动推断类型，无需手动强制转换',
    '在系统边界进行严格的空值校验，核心业务享受完整保护',
  ],
  interactiveElements: [
    { type: 'null-safety-playground', label: '🛡️ 空安全演练场' },
    { type: 'smart-casting-demo', label: '🧠 智能转换演示' },
  ],
}

// 引用元素
const topic0 = ref<HTMLElement>()
const topic1 = ref<HTMLElement>()
const topic2 = ref<HTMLElement>()
const topic3 = ref<HTMLElement>()

// 方法
const scrollToTopic = (index: number) => {
  const topics = [topic0, topic1, topic2, topic3]
  const target = topics[index]?.value
  if (target) {
    target.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const handleScroll = () => {
  const topics = [topic0, topic1, topic2, topic3]
  const scrollPosition = window.scrollY + 200

  for (let i = topics.length - 1; i >= 0; i--) {
    const element = topics[i]?.value
    if (element && element.offsetTop <= scrollPosition) {
      currentTopic.value = i
      break
    }
  }

  // 更新进度
  const totalHeight = document.documentElement.scrollHeight - window.innerHeight
  const scrolled = window.scrollY
  progress.value = Math.min((scrolled / totalHeight) * 100, 100)
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('交互类型:', type)
  // 这里可以添加具体的交互逻辑
  switch (type) {
    case 'java-kotlin-converter':
      // 显示 Java/Kotlin 代码转换器
      break
    case 'syntax-comparison':
      // 显示语法对比工具
      break
    default:
      console.log('未知交互类型:', type)
  }
}

const renderMindMap = async () => {
  try {
    // 动态导入 Mermaid
    const mermaid = (await import('mermaid')).default

    // 配置 Mermaid
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      mindmap: {
        padding: 20,
        maxNodeSizeX: 200,
        maxNodeSizeY: 100,
      },
    })

    // 创建思维导图内容
    const mindmapContent = `mindmap
  root((第九章 Kotlin))
    核心理念务实的Java增强
    便利性简洁性
      val和var
      默认只读集合
      表达式化if和when和try
      函数顶层命名默认参数
    类和对象
      属性代替字段getter和setter
      主构造函数
      dataclass自动生成equals和hashCode等
      open关键字默认final
    安全保障
      空安全NullSafety
        可空类型问号
        安全调用问号点
        Elvis操作符问号冒号
        非空断言感叹号感叹号慎用
      智能类型转换SmartCasting
    现代并发
      协程Coroutines
        轻量级线程
        suspend函数
        结构化并发
    互操作性
      百分百兼容Java
      平台类型
      注解JvmStatic和JvmOverloads`

    // 清空容器
    const container = document.getElementById('kotlin-mindmap')
    if (container) {
      container.innerHTML = ''

      // 渲染思维导图
      const { svg } = await mermaid.render('kotlin-mindmap-svg', mindmapContent)
      container.innerHTML = svg
    }
  } catch (error) {
    console.error('Mermaid 渲染错误:', error)
    const container = document.getElementById('kotlin-mindmap')
    if (container) {
      container.innerHTML =
        '<p style="color: red; text-align: center;">思维导图渲染失败，请刷新页面重试</p>'
    }
  }
}

const downloadMindMap = () => {
  const svg = document.querySelector('#kotlin-mindmap svg')
  if (svg) {
    const svgData = new XMLSerializer().serializeToString(svg)
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx?.drawImage(img, 0, 0)

      const link = document.createElement('a')
      link.download = 'kotlin-mindmap.png'
      link.href = canvas.toDataURL()
      link.click()
    }

    img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)))
  }
}

// 生命周期
onMounted(() => {
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll, { passive: true })

  // 初始化当前章节
  handleScroll()

  // 渲染思维导图
  setTimeout(() => {
    renderMindMap()
  }, 1000)

  // 模拟进度更新
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 2
    } else {
      clearInterval(interval)
    }
  }, 100)
})

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter9 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-info h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 600px;
}

/* 进度指示器 */
.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

/* 内容布局 */
.content-wrapper {
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 100px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.outline h3 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 1.5rem;
  font-size: 1.1rem;
}

.outline-grid {
  padding: 1rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 工具栏 */
.toolbar {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 0.5rem;
}

.tool-button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tool-button:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* Kotlin 简介样式 */
.kotlin-introduction-showcase {
  padding: 2rem;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.design-philosophy {
  margin: 2rem 0;
}

.design-philosophy h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

.philosophy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.philosophy-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  text-align: center;
  transition: all 0.3s ease;
}

.philosophy-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.philosophy-card h5 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.1rem;
}

.philosophy-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 代码对比样式 */
.comparison-showcase {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #fff3e0;
  border-radius: 12px;
  border: 1px solid #ffcc02;
}

.comparison-showcase h4 {
  margin: 0 0 1.5rem 0;
  color: #e65100;
  font-size: 1.3rem;
}

.code-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.comparison-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.comparison-item h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  text-align: center;
}

.code-block {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  border-left: 3px solid #667eea;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  line-height: 1.4;
}

.code-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.stat-item {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 便利性特性样式 */
.convenience-showcase {
  padding: 2rem;
}

.feature-showcase {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.feature-card h4 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.2rem;
}

.feature-comparison {
  margin: 1rem 0;
}

.before-after {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.before,
.after {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.before h6,
.after h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
  text-align: center;
}

.before pre,
.after pre {
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.4;
  white-space: pre-wrap;
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 4px;
}

.feature-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.benefit {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 实际问题解决方案样式 */
.real-world-problems {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fff3e0;
  border-radius: 12px;
  border: 1px solid #ffcc02;
}

.real-world-problems h3 {
  margin: 0 0 1rem 0;
  color: #e65100;
}

.problem-solution {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.problem,
.root-cause,
.solutions {
  padding: 1rem;
  border-radius: 8px;
}

.problem {
  background: #ffebee;
  border-left: 4px solid #f44336;
}

.root-cause {
  background: #fff3e0;
  border-left: 4px solid #ff9800;
}

.solutions {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.problem h4,
.root-cause h4,
.solutions h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.solution-guidelines {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.guideline {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.guideline h5 {
  margin: 0 0 0.5rem 0;
  color: #4caf50;
}

.guideline p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.code-example {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  border-left: 3px solid #667eea;
}

.code-example h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.8rem;
  font-family: inherit;
}

.code-example pre {
  margin: 0;
  white-space: pre-wrap;
  line-height: 1.4;
}

.best-practice-note {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #4caf50;
  margin-top: 1rem;
  font-size: 0.9rem;
}

/* 类与对象样式 */
.classes-objects-showcase {
  padding: 2rem;
}

.feature-comparison-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

.feature-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.feature-item h4 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.2rem;
}

.java-version,
.kotlin-version {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.java-version h5,
.kotlin-version h5 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.auto-generated {
  margin-top: 1rem;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 6px;
  border-left: 3px solid #4caf50;
}

.auto-generated h6 {
  margin: 0 0 0.5rem 0;
  color: #2e7d32;
  font-size: 0.9rem;
}

.auto-generated ul {
  margin: 0;
  padding-left: 1.5rem;
}

.auto-generated li {
  margin: 0.25rem 0;
  font-size: 0.85rem;
  color: #2e7d32;
}

.inheritance-demo,
.properties-demo {
  margin-top: 1rem;
}

.concept-explanation {
  background: #fff3e0;
  padding: 1rem;
  border-radius: 6px;
  border-left: 3px solid #ff9800;
  margin-bottom: 1rem;
}

.concept-explanation p {
  margin: 0;
  color: #e65100;
  font-size: 0.9rem;
}

.concept-explanation code {
  background: rgba(255, 152, 0, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.solution-demo {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.solution-demo pre {
  margin: 0;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  line-height: 1.4;
  overflow-x: auto;
}

.benefits-list {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.benefits-list h6 {
  margin: 0 0 0.5rem 0;
  color: #4caf50;
  font-size: 0.9rem;
}

.benefits-list ul {
  margin: 0;
  padding-left: 1.5rem;
}

.benefits-list li {
  margin: 0.25rem 0;
  font-size: 0.85rem;
  color: #2e7d32;
}

/* 空安全样式 */
.null-safety-showcase {
  padding: 2rem;
}

.safety-features {
  margin-top: 2rem;
}

.feature-section {
  margin-bottom: 2rem;
}

.feature-section h4 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.2rem;
}

.type-system {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.type-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 2px solid;
}

.type-card.non-null {
  border-color: #4caf50;
  background: #f1f8e9;
}

.type-card.nullable {
  border-color: #ff9800;
  background: #fff3e0;
}

.type-card h5 {
  margin: 0 0 1rem 0;
  text-align: center;
  font-size: 1rem;
}

.type-guarantee {
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-top: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
}

.type-card.non-null .type-guarantee {
  background: #c8e6c9;
  color: #2e7d32;
}

.type-card.nullable .type-guarantee {
  background: #ffcc02;
  color: #e65100;
}

.operator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.operator-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.operator-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.operator-card.warning {
  border-color: #f44336;
  background: #ffebee;
}

.operator-card h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
}

.operator-card.warning h6 {
  color: #d32f2f;
}

.analogy {
  font-size: 0.8rem;
  font-style: italic;
  color: #666;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.smart-casting-demo {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.smart-casting-explanation {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 6px;
  border-left: 3px solid #2196f3;
  margin-top: 1rem;
}

.smart-casting-explanation p {
  margin: 0;
  color: #1565c0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.solution-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.step {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.step h5 {
  margin: 0 0 0.5rem 0;
  color: #4caf50;
  font-size: 1rem;
}

.step p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* 思维导图样式 */
.mindmap-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
  overflow: hidden;
}

.mindmap-container {
  padding: 2rem;
}

.mindmap-container h3 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
  font-size: 1.5rem;
  text-align: center;
}

.mindmap-description {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
  font-style: italic;
}

.mindmap-canvas {
  min-height: 400px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.mindmap-canvas svg {
  max-width: 100%;
  height: auto;
}

.mindmap-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.mindmap-button {
  padding: 0.75rem 1.5rem;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.mindmap-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
  }

  .philosophy-grid {
    grid-template-columns: 1fr;
  }

  .code-comparison {
    grid-template-columns: 1fr;
  }

  .before-after {
    grid-template-columns: 1fr;
  }
}
</style>
