<template>
  <div class="java-chapter9">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <div class="chapter-info">
            <h1 class="chapter-title">第九章：Kotlin</h1>
            <p class="chapter-subtitle">Modern JVM Language</p>
            <p class="chapter-description">
              探索现代化JVM语言Kotlin：从设计哲学到实践应用，掌握这门Java最亲密战友的核心特性与最佳实践
            </p>
          </div>
          <div class="progress-indicator">
            <div class="progress-circle">
              <svg class="progress-ring" width="120" height="120">
                <circle
                  class="progress-ring-circle"
                  stroke="rgba(255,255,255,0.3)"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                />
                <circle
                  class="progress-ring-circle progress-ring-circle-fill"
                  stroke="white"
                  stroke-width="4"
                  fill="transparent"
                  r="52"
                  cx="60"
                  cy="60"
                  :stroke-dasharray="`${2 * Math.PI * 52}`"
                  :stroke-dashoffset="`${2 * Math.PI * 52 * (1 - progress / 100)}`"
                />
              </svg>
              <div class="progress-text">{{ Math.round(progress) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: Kotlin 简介与核心理念 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="Kotlin 简介与核心理念 (Introduction &amp; Philosophy)"
                :concept-data="kotlinIntroductionData"
                @interaction="handleInteraction"
              >
                <div class="kotlin-introduction-showcase">
                  <h3>🎯 Kotlin：Java 的现代化战友</h3>
                  <p class="intro-text">
                    Kotlin 不是为了颠覆 Java，而是作为 Java 最亲密的"战友"和"增强补丁"而生。
                    它解决了 Java
                    因背负历史包袱而难以解决的许多痛点，让我们写出更简洁、更安全、也更富表现力的代码。
                  </p>

                  <div class="design-philosophy">
                    <h4>🏗️ 核心设计哲学</h4>
                    <div class="philosophy-grid">
                      <div class="philosophy-card">
                        <div class="card-icon">🎯</div>
                        <h5>务实主义 (Pragmatism)</h5>
                        <p>
                          每一个特性都旨在解决 Java
                          开发中的真实痛点，不追求学术完美，而是追求工程好用
                        </p>
                      </div>
                      <div class="philosophy-card">
                        <div class="card-icon">🔗</div>
                        <h5>100% 互操作性</h5>
                        <p>与 Java 完全兼容，可以逐个文件迁移，所有 Java 库和框架都能直接使用</p>
                      </div>
                      <div class="philosophy-card">
                        <div class="card-icon">🛡️</div>
                        <h5>类型安全</h5>
                        <p>在类型系统层面根除 NullPointerException，提供编译期安全保障</p>
                      </div>
                      <div class="philosophy-card">
                        <div class="card-icon">✨</div>
                        <h5>简洁表达</h5>
                        <p>大幅减少模板代码，让核心业务逻辑更加突出和清晰</p>
                      </div>
                    </div>
                  </div>

                  <div class="comparison-showcase">
                    <h4>📊 Java vs Kotlin：一目了然的对比</h4>
                    <div class="code-comparison">
                      <div class="comparison-item">
                        <h5>Java 版本</h5>
                        <div class="code-block">
                          <pre><code>public class User {
    private final String name;
    private final int age;
    
    public User(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public String getName() { return name; }
    public int getAge() { return age; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return age == user.age && Objects.equals(name, user.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, age);
    }
    
    @Override
    public String toString() {
        return "User{name='" + name + "', age=" + age + "}";
    }
}</code></pre>
                        </div>
                        <div class="code-stats">
                          <span class="stat-item">📏 25+ 行代码</span>
                          <span class="stat-item">⚠️ 容易出错</span>
                        </div>
                      </div>
                      <div class="comparison-item">
                        <h5>Kotlin 版本</h5>
                        <div class="code-block">
                          <pre><code>data class User(val name: String, val age: Int)</code></pre>
                        </div>
                        <div class="code-stats">
                          <span class="stat-item">📏 1 行代码</span>
                          <span class="stat-item">✅ 自动生成所有方法</span>
                          <span class="stat-item">🛡️ 类型安全</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 便利性与简洁性 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="便利性与简洁性 (Convenience &amp; Conciseness)"
                :concept-data="convenienceData"
                @interaction="handleInteraction"
              >
                <div class="convenience-showcase">
                  <h3>✨ 用更少的代码，干同样的事</h3>
                  <p class="intro-text">
                    Kotlin 通过引入一系列语法糖和现代语言特性，大幅减少编写 Java
                    代码时所需的"模板代码"， 让代码更紧凑、更易读，同时保持清晰的业务逻辑表达。
                  </p>

                  <div class="feature-showcase">
                    <div class="feature-card">
                      <h4>🔧 val/var：更智能的变量声明</h4>
                      <div class="feature-comparison">
                        <div class="before-after">
                          <div class="before">
                            <h6>Java</h6>
                            <pre><code>final String name = "Kotlin";
String message = "Hello";
message = "Hi";</code></pre>
                          </div>
                          <div class="after">
                            <h6>Kotlin</h6>
                            <pre><code>val name = "Kotlin"  // 不可变
var message = "Hello" // 可变
message = "Hi"</code></pre>
                          </div>
                        </div>
                      </div>
                      <div class="feature-benefits">
                        <span class="benefit">🎯 类型推断</span>
                        <span class="benefit">🛡️ 鼓励不可变性</span>
                        <span class="benefit">📝 代码更简洁</span>
                      </div>
                    </div>

                    <div class="feature-card">
                      <h4>🎭 表达式化控制流</h4>
                      <div class="feature-comparison">
                        <div class="before-after">
                          <div class="before">
                            <h6>Java</h6>
                            <pre><code>String grade;
if (score &gt;= 90) {
    grade = "A";
} else if (score &gt;= 60) {
    grade = "B";
} else {
    grade = "C";
}</code></pre>
                          </div>
                          <div class="after">
                            <h6>Kotlin</h6>
                            <pre><code>val grade = if (score &gt;= 90) "A" 
           else if (score &gt;= 60) "B" 
           else "C"</code></pre>
                          </div>
                        </div>
                      </div>
                      <div class="feature-benefits">
                        <span class="benefit">🎯 表达式返回值</span>
                        <span class="benefit">📝 减少重复赋值</span>
                        <span class="benefit">🔒 避免未初始化变量</span>
                      </div>
                    </div>
                  </div>

                  <div class="real-world-problems">
                    <h3>🚨 实际项目踩坑与解决方案</h3>
                    <div class="problem-solution">
                      <div class="problem">
                        <h4>常见问题：滥用隐式参数 `it` 导致可读性下降</h4>
                        <p>
                          一个 Java 项目迁移到 Kotlin
                          后，团队成员发现虽然代码行数减少了，但代码变得难以理解。
                          尤其是集合操作，大量使用了 `it`
                          作为隐式参数的链式调用，当链条很长、逻辑复杂时， 新手很难快速看出每个 `it`
                          指代的到底是什么。
                        </p>
                        <div class="code-example">
                          <h6>❌ 问题代码</h6>
                          <pre><code>list.filter { it > 0 }
    .map { it.toString() }
    .forEach {
        println("Processing: $it")
        anotherList.filter { it > 0 } // 这里的 it 是哪个？
    }</code></pre>
                        </div>
                      </div>
                      <div class="root-cause">
                        <h4>根源分析</h4>
                        <p>
                          滥用了隐式参数 `it`。`it` 是 Kotlin 为只有一个参数的 Lambda
                          提供的便利语法， 旨在简化代码。但在嵌套 Lambda 或长调用链中，多个 `it`
                          的存在会造成严重的指代不明和可读性下降。
                        </p>
                      </div>
                      <div class="solutions">
                        <h4>业界最佳实践：制定明确的编码规范</h4>
                        <div class="solution-guidelines">
                          <div class="guideline">
                            <h5>✅ 简单场景：允许使用 `it`</h5>
                            <p>对于单层、且逻辑极度简单的 Lambda</p>
                            <div class="code-example">
                              <pre><code>names.map { it.uppercase() }
numbers.filter { it > 0 }</code></pre>
                            </div>
                          </div>
                          <div class="guideline">
                            <h5>🎯 复杂场景：必须显式命名</h5>
                            <p>在任何嵌套的 Lambda 中，或者当 Lambda 体超过一行时</p>
                            <div class="code-example">
                              <pre><code>// 推荐写法
list.forEach { item ->
    println("Processing item: $item")
    anotherList.filter { number -> number > 0 }
}</code></pre>
                            </div>
                          </div>
                        </div>
                        <div class="best-practice-note">
                          <strong>核心原则：</strong>清晰性永远优先于简洁性。 合理使用显式命名是区分
                          Kotlin 新手和专家的重要标志。
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: 'Kotlin 简介与核心理念',
    description: '理解 Kotlin 的设计哲学和核心价值',
  },
  {
    title: '便利性与简洁性',
    description: '探索 Kotlin 如何简化 Java 的冗余代码',
  },
  {
    title: '焕然一新的类与对象',
    description: '学习 Kotlin 的现代化面向对象特性',
  },
  {
    title: '编码安全感：空安全与智能类型转换',
    description: '掌握 Kotlin 的类型安全机制',
  },
  {
    title: '现代并发模型：协程',
    description: '初探 Kotlin 的轻量级并发方案',
  },
  {
    title: '无缝集成：与 Java 的互操作性',
    description: '学习 Kotlin 与 Java 的完美共存',
  },
]

// 概念数据
const kotlinIntroductionData = {
  keyPoints: [
    'Kotlin 是 Java 的现代化增强补丁，不是替代品',
    '务实主义设计哲学：解决真实痛点而非追求学术完美',
    '100% Java 互操作性，可以逐步迁移现有项目',
    '类型安全设计，在编译期根除 NullPointerException',
    '大幅减少模板代码，提升开发效率和代码可读性',
  ],
  interactiveElements: [
    { type: 'java-kotlin-converter', label: '🔄 Java/Kotlin 代码转换器' },
    { type: 'interop-demo', label: '🔗 互操作性演示' },
  ],
}

const convenienceData = {
  keyPoints: [
    'val/var 关键字提供更智能的变量声明和类型推断',
    '表达式化控制流让代码更简洁和安全',
    '约定优于配置的设计减少样板代码',
    '函数式编程特性增强代码表达力',
    '合理使用简洁特性，清晰性优先于简洁性',
  ],
  interactiveElements: [
    { type: 'syntax-comparison', label: '📊 语法对比工具' },
    { type: 'code-golf', label: '⛳ 代码简化挑战' },
  ],
}

// 引用元素
const topic0 = ref<HTMLElement>()
const topic1 = ref<HTMLElement>()

// 方法
const scrollToTopic = (index: number) => {
  const topics = [topic0, topic1]
  const target = topics[index]?.value
  if (target) {
    target.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const handleScroll = () => {
  const topics = [topic0, topic1]
  const scrollPosition = window.scrollY + 200

  for (let i = topics.length - 1; i >= 0; i--) {
    const element = topics[i]?.value
    if (element && element.offsetTop <= scrollPosition) {
      currentTopic.value = i
      break
    }
  }

  // 更新进度
  const totalHeight = document.documentElement.scrollHeight - window.innerHeight
  const scrolled = window.scrollY
  progress.value = Math.min((scrolled / totalHeight) * 100, 100)
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('交互类型:', type)
  // 这里可以添加具体的交互逻辑
  switch (type) {
    case 'java-kotlin-converter':
      // 显示 Java/Kotlin 代码转换器
      break
    case 'syntax-comparison':
      // 显示语法对比工具
      break
    default:
      console.log('未知交互类型:', type)
  }
}

// 生命周期
onMounted(() => {
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll, { passive: true })

  // 初始化当前章节
  handleScroll()

  // 模拟进度更新
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 2
    } else {
      clearInterval(interval)
    }
  }, 100)
})

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter9 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.chapter-info h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  font-style: italic;
}

.chapter-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 600px;
}

/* 进度指示器 */
.progress-indicator {
  position: relative;
}

.progress-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle-fill {
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 600;
}

/* 内容布局 */
.content-wrapper {
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 100px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.outline h3 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 1.5rem;
  font-size: 1.1rem;
}

.outline-grid {
  padding: 1rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
  transform: translateX(5px);
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* 工具栏 */
.toolbar {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 0.5rem;
}

.tool-button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tool-button:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* Kotlin 简介样式 */
.kotlin-introduction-showcase {
  padding: 2rem;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.design-philosophy {
  margin: 2rem 0;
}

.design-philosophy h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

.philosophy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.philosophy-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  text-align: center;
  transition: all 0.3s ease;
}

.philosophy-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.philosophy-card h5 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.1rem;
}

.philosophy-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 代码对比样式 */
.comparison-showcase {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #fff3e0;
  border-radius: 12px;
  border: 1px solid #ffcc02;
}

.comparison-showcase h4 {
  margin: 0 0 1.5rem 0;
  color: #e65100;
  font-size: 1.3rem;
}

.code-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.comparison-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.comparison-item h5 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  text-align: center;
}

.code-block {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  border-left: 3px solid #667eea;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  line-height: 1.4;
}

.code-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.stat-item {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 便利性特性样式 */
.convenience-showcase {
  padding: 2rem;
}

.feature-showcase {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.feature-card h4 {
  margin: 0 0 1rem 0;
  color: #667eea;
  font-size: 1.2rem;
}

.feature-comparison {
  margin: 1rem 0;
}

.before-after {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.before,
.after {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.before h6,
.after h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
  text-align: center;
}

.before pre,
.after pre {
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.4;
  white-space: pre-wrap;
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 4px;
}

.feature-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.benefit {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 实际问题解决方案样式 */
.real-world-problems {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fff3e0;
  border-radius: 12px;
  border: 1px solid #ffcc02;
}

.real-world-problems h3 {
  margin: 0 0 1rem 0;
  color: #e65100;
}

.problem-solution {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.problem,
.root-cause,
.solutions {
  padding: 1rem;
  border-radius: 8px;
}

.problem {
  background: #ffebee;
  border-left: 4px solid #f44336;
}

.root-cause {
  background: #fff3e0;
  border-left: 4px solid #ff9800;
}

.solutions {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.problem h4,
.root-cause h4,
.solutions h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.solution-guidelines {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.guideline {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.guideline h5 {
  margin: 0 0 0.5rem 0;
  color: #4caf50;
}

.guideline p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.code-example {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  border-left: 3px solid #667eea;
}

.code-example h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.8rem;
  font-family: inherit;
}

.code-example pre {
  margin: 0;
  white-space: pre-wrap;
  line-height: 1.4;
}

.best-practice-note {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border-left: 4px solid #4caf50;
  margin-top: 1rem;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
  }

  .philosophy-grid {
    grid-template-columns: 1fr;
  }

  .code-comparison {
    grid-template-columns: 1fr;
  }

  .before-after {
    grid-template-columns: 1fr;
  }
}
</style>
